import React from "react";

function CountryInfoPanel({ selectedCountryData }) {
    if (!selectedCountryData) {
        return (
            <div className="w-full bg-white h-[100vh] overflow-y-auto p-4 border border-gray-300 rounded-md shadow-md">
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-12 w-12 mb-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
                        />
                    </svg>
                    <p className="text-lg font-medium">
                        Select a country on the map
                    </p>
                    <p className="text-sm">
                        Click on any country to view detailed information
                    </p>
                </div>
            </div>
        );
    }
    /* [
    {
        "name": {
            "common": "Austria",
            "official": "Republic of Austria",
            "nativeName": {
                "bar": {
                    "official": "Republik Österreich",
                    "common": "Österreich"
                }
            }
        },
        "tld": [
            ".at"
        ],
        "cca2": "AT",
        "ccn3": "040",
        "cioc": "AUT",
        "independent": true,
        "status": "officially-assigned",
        "unMember": true,
        "currencies": {
            "EUR": {
                "symbol": "€",
                "name": "Euro"
            }
        },
        "idd": {
            "root": "+4",
            "suffixes": [
                "3"
            ]
        },
        "capital": [
            "Vienna"
        ],
        "altSpellings": [
            "AT",
            "Osterreich",
            "Oesterreich"
        ],
        "region": "Europe",
        "subregion": "Central Europe",
        "languages": {
            "deu": "German"
        },
        "latlng": [
            47.33333333,
            13.33333333
        ],
        "landlocked": true,
        "borders": [
            "CZE",
            "DEU",
            "HUN",
            "ITA",
            "LIE",
            "SVK",
            "SVN",
            "CHE"
        ],
        "area": 83871,
        "demonyms": {
            "eng": {
                "f": "Austrian",
                "m": "Austrian"
            },
            "fra": {
                "f": "Autrichienne",
                "m": "Autrichien"
            }
        },
        "cca3": "AUT",
        "translations": {
            "ara": {
                "official": "جمهورية النمسا",
                "common": "النمسا"
            },
            "bre": {
                "official": "Republik Aostria",
                "common": "Aostria"
            },
            "ces": {
                "official": "Rakouská republika",
                "common": "Rakousko"
            },
            "cym": {
                "official": "Gweriniaeth Awstria",
                "common": "Awstria"
            },
            "deu": {
                "official": "Republik Österreich",
                "common": "Österreich"
            },
            "est": {
                "official": "Austria Vabariik",
                "common": "Austria"
            },
            "fin": {
                "official": "Itävallan tasavalta",
                "common": "Itävalta"
            },
            "fra": {
                "official": "République d'Autriche",
                "common": "Autriche"
            },
            "hrv": {
                "official": "Republika Austrija",
                "common": "Austrija"
            },
            "hun": {
                "official": "Ausztria",
                "common": "Ausztria"
            },
            "ind": {
                "official": "Republik Austria",
                "common": "Austria"
            },
            "ita": {
                "official": "Repubblica d'Austria",
                "common": "Austria"
            },
            "jpn": {
                "official": "オーストリア共和国",
                "common": "オーストリア"
            },
            "kor": {
                "official": "오스트리아 공화국",
                "common": "오스트리아"
            },
            "nld": {
                "official": "Republiek Oostenrijk",
                "common": "Oostenrijk"
            },
            "per": {
                "official": "جمهوری اتریش",
                "common": "اتریش"
            },
            "pol": {
                "official": "Republika Austrii",
                "common": "Austria"
            },
            "por": {
                "official": "República da Áustria",
                "common": "Áustria"
            },
            "rus": {
                "official": "Австрийская Республика",
                "common": "Австрия"
            },
            "slk": {
                "official": "Rakúska republika",
                "common": "Rakúsko"
            },
            "spa": {
                "official": "República de Austria",
                "common": "Austria"
            },
            "srp": {
                "official": "Република Аустрија",
                "common": "Аустрија"
            },
            "swe": {
                "official": "Republiken Österrike",
                "common": "Österrike"
            },
            "tur": {
                "official": "Avusturya Cumhuriyeti",
                "common": "Avusturya"
            },
            "urd": {
                "official": "جمہوریہ آسٹریا",
                "common": "آسٹریا"
            },
            "zho": {
                "official": "奥地利共和国",
                "common": "奥地利"
            }
        },
        "flag": "🇦🇹",
        "maps": {
            "googleMaps": "https://goo.gl/maps/pCWpWQhznHyRzQcu9",
            "openStreetMaps": "https://www.openstreetmap.org/relation/16239"
        },
        "population": 8917205,
        "gini": {
            "2018": 30.8
        },
        "fifa": "AUT",
        "car": {
            "signs": [
                "A"
            ],
            "side": "right"
        },
        "timezones": [
            "UTC+01:00"
        ],
        "continents": [
            "Europe"
        ],
        "flags": {
            "png": "https://flagcdn.com/w320/at.png",
            "svg": "https://flagcdn.com/at.svg",
            "alt": "The flag of Austria is composed of three equal horizontal bands of red, white and red."
        },
        "coatOfArms": {
            "png": "https://mainfacts.com/media/images/coats_of_arms/at.png",
            "svg": "https://mainfacts.com/media/images/coats_of_arms/at.svg"
        },
        "startOfWeek": "monday",
        "capitalInfo": {
            "latlng": [
                48.2,
                16.37
            ]
        },
        "postalCode": {
            "format": "####",
            "regex": "^(\\d{4})$"
        }
    }
] */
    return selectedCountryData.map((selectedCountry) => {
        <div className="w-full bg-white h-[100vh] overflow-y-auto p-4 border border-gray-300 rounded-md shadow-md">
            <div className="space-y-4">
                <h1 className="text-2xl font-bold text-gray-800">
                    {selectedCountry.altSpelling}
                </h1>

                {countriesData &&
                    countriesData.features
                        .filter(
                            (country) =>
                                country.properties.name === selectedCountry
                        )
                        .map((country) => (
                            <div
                                key={country.properties.name}
                                className="space-y-4"
                            >
                                {/* Basic Information */}
                                <div className="bg-gray-50 p-3 rounded-lg">
                                    <h2 className="text-lg font-semibold text-gray-700 border-b pb-1 mb-2">
                                        Basic Information
                                    </h2>
                                    <div className="grid grid-cols-2 gap-2 text-sm">
                                        <div className="font-medium">
                                            Official Name:
                                        </div>
                                        <div>
                                            {country.properties.name_long ||
                                                country.properties.name}
                                        </div>

                                        <div className="font-medium">
                                            ISO Codes:
                                        </div>
                                        <div>
                                            {country.properties.iso_a2}/
                                            {country.properties.iso_a3 || "N/A"}
                                        </div>

                                        <div className="font-medium">
                                            Region:
                                        </div>
                                        <div>
                                            {country.properties.region ||
                                                country.properties.continent ||
                                                "N/A"}
                                        </div>

                                        <div className="font-medium">
                                            Subregion:
                                        </div>
                                        <div>
                                            {country.properties.subregion ||
                                                "N/A"}
                                        </div>
                                    </div>
                                </div>

                                {/* Geography */}
                                <div className="bg-blue-50 p-3 rounded-lg">
                                    <h2 className="text-lg font-semibold text-gray-700 border-b pb-1 mb-2">
                                        Geography
                                    </h2>
                                    <div className="grid grid-cols-2 gap-2 text-sm">
                                        <div className="font-medium">Area:</div>
                                        <div>
                                            {country.properties.area_km2 ||
                                                "Calculating..."}{" "}
                                            km²
                                        </div>

                                        <div className="font-medium">
                                            Capital:
                                        </div>
                                        <div>
                                            {country.properties.capital ||
                                                "N/A"}
                                        </div>

                                        <div className="font-medium">
                                            Borders:
                                        </div>
                                        <div>
                                            {country.properties.borders
                                                ? country.properties.borders.join(
                                                      ", "
                                                  )
                                                : "Island nation"}
                                        </div>

                                        <div className="font-medium">
                                            Coastline:
                                        </div>
                                        <div>
                                            {country.properties.coastline
                                                ? "Yes"
                                                : "Landlocked"}
                                        </div>
                                    </div>
                                </div>

                                {/* Demographics */}
                                <div className="bg-green-50 p-3 rounded-lg">
                                    <h2 className="text-lg font-semibold text-gray-700 border-b pb-1 mb-2">
                                        Demographics
                                    </h2>
                                    <div className="grid grid-cols-2 gap-2 text-sm">
                                        <div className="font-medium">
                                            Population:
                                        </div>
                                        <div>
                                            {country.properties.pop_est
                                                ? new Intl.NumberFormat().format(
                                                      country.properties.pop_est
                                                  )
                                                : "N/A"}
                                        </div>

                                        <div className="font-medium">
                                            Languages:
                                        </div>
                                        <div>
                                            {country.properties.languages ||
                                                "N/A"}
                                        </div>

                                        <div className="font-medium">
                                            Currency:
                                        </div>
                                        <div>
                                            {country.properties.currency ||
                                                "N/A"}
                                        </div>

                                        <div className="font-medium">GDP:</div>
                                        <div>
                                            {country.properties.gdp_md_est
                                                ? `$${country.properties.gdp_md_est} million`
                                                : "N/A"}
                                        </div>
                                    </div>
                                </div>

                                {/* Weather & Climate */}
                                <div className="bg-yellow-50 p-3 rounded-lg">
                                    <h2 className="text-lg font-semibold text-gray-700 border-b pb-1 mb-2">
                                        Weather & Climate
                                    </h2>
                                    <div className="flex justify-between items-center">
                                        <div className="text-sm">
                                            <p>
                                                Current weather data would be
                                                fetched from a weather API.
                                            </p>
                                            <p className="mt-1">
                                                Possible data: temperature,
                                                conditions, forecast, climate
                                                zones
                                            </p>
                                        </div>
                                        <button
                                            className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition"
                                            onClick={() =>
                                                alert(
                                                    `Would fetch weather for ${selectedCountry}`
                                                )
                                            }
                                        >
                                            Get Weather
                                        </button>
                                    </div>
                                </div>

                                {/* External Resources */}
                                <div className="bg-purple-50 p-3 rounded-lg">
                                    <h2 className="text-lg font-semibold text-gray-700 border-b pb-1 mb-2">
                                        Learn More
                                    </h2>
                                    <div className="flex flex-wrap gap-2">
                                        <a
                                            href={`https://en.wikipedia.org/wiki/${selectedCountry}`}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="px-3 py-1 bg-gray-200 text-gray-800 text-sm rounded hover:bg-gray-300 transition"
                                        >
                                            Wikipedia
                                        </a>
                                        <a
                                            href={`https://www.google.com/maps/place/${selectedCountry}`}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="px-3 py-1 bg-gray-200 text-gray-800 text-sm rounded hover:bg-gray-300 transition"
                                        >
                                            Google Maps
                                        </a>
                                        <a
                                            href={`https://www.cia.gov/the-world-factbook/countries/${selectedCountry
                                                .toLowerCase()
                                                .replace(/\s+/g, "-")}/`}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="px-3 py-1 bg-gray-200 text-gray-800 text-sm rounded hover:bg-gray-300 transition"
                                        >
                                            CIA World Factbook
                                        </a>
                                    </div>
                                </div>
                            </div>
                        ))}
            </div>
        </div>;
    });
}

export default CountryInfoPanel;
