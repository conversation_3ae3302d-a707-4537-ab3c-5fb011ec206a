import React, { useEffect, useState } from "react";
import { OPENWEATHERMAP_API_KEY } from "../../constants";
import Image from "next/image";

function CountryInfoPanel({ selectedCountry }) {
    const [countryData, setCountryData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [loadingWeather, setLoadingWeather] = useState(false);
    const [countryWeather, setCountryWeather] = useState(null);
    const [coordinates, setCoordinates] = useState([]);

    useEffect(() => {
        if (coordinates.length >= 2) {
            fetch(
                `https://api.openweathermap.org/data/2.5/weather?lat=${coordinates[0]}&lon=${coordinates[1]}&appid=${OPENWEATHERMAP_API_KEY}`
            )
                .then((response) => response.json)
                .then((data) => {
                    console.log(data);
                });
        }
    }, [coordinates]);

    useEffect(() => {
        if (selectedCountry) {
            setLoading(true);
            fetch(`https://restcountries.com/v3.1/name/${selectedCountry}`)
                .then((response) => response.json())
                .then((data) => {
                    setCountryData(data[0]);
                    setCoordinates([data[0].latlng[0], data[0].latlng[1]]);
                    setLoading(false);
                    console.log(data);
                })
                .catch((error) => {
                    console.error("Error fetching country details:", error);
                    setLoading(false);
                });
        } else {
            setCoordinates([]);
            setCountryData(null);
        }
    }, [selectedCountry]);

    if (!selectedCountry) {
        return (
            <div className="w-full bg-white h-[100vh] overflow-y-auto p-4 border border-gray-300 rounded-md shadow-md">
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-12 w-12 mb-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
                        />
                    </svg>
                    <p className="text-lg font-medium">
                        Select a country on the map
                    </p>
                    <p className="text-sm">
                        Click on any country to view detailed information
                    </p>
                </div>
            </div>
        );
    }

    if (loading) {
        return (
            <div className="w-full bg-white h-[100vh] overflow-y-auto p-4 border border-gray-300 rounded-md shadow-md">
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                    <p className="text-lg font-medium mt-4">
                        Loading country information...
                    </p>
                </div>
            </div>
        );
    }

    if (!countryData) {
        return (
            <div className="w-full bg-white h-[100vh] overflow-y-auto p-4 border border-gray-300 rounded-md shadow-md">
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <p className="text-lg font-medium">Country not found</p>
                    <p className="text-sm">
                        Unable to load information for {selectedCountry}
                    </p>
                </div>
            </div>
        );
    }
    // Helper functions to extract data from the REST Countries API response
    const getLanguages = () => {
        if (!countryData.languages) return "N/A";
        return Object.values(countryData.languages).join(", ");
    };

    const getCurrencies = () => {
        if (!countryData.currencies) return "N/A";
        return Object.values(countryData.currencies)
            .map((currency) => `${currency.name} (${currency.symbol})`)
            .join(", ");
    };

    const getBorders = () => {
        if (!countryData.borders || countryData.borders.length === 0) {
            return countryData.landlocked ? "Landlocked" : "Island nation";
        }
        return countryData.borders.join(", ");
    };

    return (
        <div className="w-full bg-white h-[100vh] overflow-y-auto p-4 border border-gray-300 rounded-md shadow-md">
            <div className="space-y-4">
                <div className="flex justify-around border-b-2 items-center gap-3">
                    <h1 className="text-2xl font-bold text-gray-800">
                        {countryData.name.common}
                    </h1>
                    <Image
                        src={countryData.flags.png}
                        alt={`${countryData.name.common} flag`}
                        width={20}
                        height={13}
                    />
                    <h1 className="text-4xl">{countryData.flag}</h1>
                </div>

                {/* Weather & Climate */}
                <div className="bg-yellow-50 p-3 rounded-lg">
                    <h2 className="text-lg font-semibold text-gray-700 border-b pb-1 mb-2">
                        Weather & Climate
                    </h2>
                    <div className="flex justify-between items-center">
                        <div className="text-sm">
                            <p>
                                Current weather data would be fetched from a
                                weather API.
                            </p>
                            <p className="mt-1">
                                Possible data: temperature, conditions,
                                forecast, climate zones
                            </p>
                        </div>
                    </div>
                </div>

                {/* Basic Information */}
                <div className="bg-gray-50 p-3 rounded-lg">
                    <h2 className="text-lg font-semibold text-gray-700 border-b pb-1 mb-2">
                        Basic Information
                    </h2>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="font-medium">Official Name:</div>
                        <div>{countryData.name.official}</div>

                        <div className="font-medium">ISO Codes:</div>
                        <div>
                            {countryData.cca2}/{countryData.cca3}
                        </div>

                        <div className="font-medium">Region:</div>
                        <div>{countryData.region}</div>

                        <div className="font-medium">Subregion:</div>
                        <div>{countryData.subregion || "N/A"}</div>

                        <div className="font-medium">UN Member:</div>
                        <div>{countryData.unMember ? "Yes" : "No"}</div>
                    </div>
                </div>

                {/* Geography */}
                <div className="bg-blue-50 p-3 rounded-lg">
                    <h2 className="text-lg font-semibold text-gray-700 border-b pb-1 mb-2">
                        Geography
                    </h2>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="font-medium">Area:</div>
                        <div>
                            {new Intl.NumberFormat().format(countryData.area)}{" "}
                            km²
                        </div>

                        <div className="font-medium">Capital:</div>
                        <div>
                            {countryData.capital
                                ? countryData.capital.join(", ")
                                : "N/A"}
                        </div>

                        <div className="font-medium">Borders:</div>
                        <div>{getBorders()}</div>

                        <div className="font-medium">Landlocked:</div>
                        <div>{countryData.landlocked ? "Yes" : "No"}</div>

                        <div className="font-medium">Coordinates:</div>
                        <div>
                            {countryData.latlng
                                ? `${countryData.latlng[0]}°, ${countryData.latlng[1]}°`
                                : "N/A"}
                        </div>
                    </div>
                </div>

                {/* Demographics */}
                <div className="bg-green-50 p-3 rounded-lg">
                    <h2 className="text-lg font-semibold text-gray-700 border-b pb-1 mb-2">
                        Demographics
                    </h2>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="font-medium">Population:</div>
                        <div>
                            {new Intl.NumberFormat().format(
                                countryData.population
                            )}
                        </div>

                        <div className="font-medium">Languages:</div>
                        <div>{getLanguages()}</div>

                        <div className="font-medium">Currency:</div>
                        <div>{getCurrencies()}</div>

                        <div className="font-medium">Timezones:</div>
                        <div>
                            {countryData.timezones
                                ? countryData.timezones.join(", ")
                                : "N/A"}
                        </div>
                    </div>
                </div>

                {/* External Resources */}
                <div className="bg-purple-50 p-3 rounded-lg">
                    <h2 className="text-lg font-semibold text-gray-700 border-b pb-1 mb-2">
                        Learn More
                    </h2>
                    <div className="flex flex-wrap gap-2">
                        <a
                            href={`https://en.wikipedia.org/wiki/${countryData.name.common}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="px-3 py-1 bg-gray-200 text-gray-800 text-sm rounded hover:bg-gray-300 transition"
                        >
                            Wikipedia
                        </a>
                        <a
                            href={
                                countryData.maps?.googleMaps ||
                                `https://www.google.com/maps/place/${countryData.name.common}`
                            }
                            target="_blank"
                            rel="noopener noreferrer"
                            className="px-3 py-1 bg-gray-200 text-gray-800 text-sm rounded hover:bg-gray-300 transition"
                        >
                            Google Maps
                        </a>
                        <a
                            href={`https://www.cia.gov/the-world-factbook/countries/${countryData.name.common
                                .toLowerCase()
                                .replace(/\s+/g, "-")}/`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="px-3 py-1 bg-gray-200 text-gray-800 text-sm rounded hover:bg-gray-300 transition"
                        >
                            CIA World Factbook
                        </a>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default CountryInfoPanel;
