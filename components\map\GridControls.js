import React from "react";

function GridControls({
    showCoordinateGrid,
    setShowCoordinateGrid,

    gridSpacing,
    setGridSpacing,
}) {
    return (
        <div className="absolute top-4 right-4 z-[1000] bg-white p-4 rounded-lg shadow-lg border">
            <h3 className="font-bold text-sm mb-3">Grid Controls</h3>

            {/* Coordinate Grid Controls */}
            <div className="mb-4">
                <label className="flex items-center mb-2">
                    <input
                        type="checkbox"
                        checked={showCoordinateGrid}
                        onChange={(e) =>
                            setShowCoordinateGrid(e.target.checked)
                        }
                        className="mr-2"
                    />
                    <span className="text-sm">Show Coordinate Grid</span>
                </label>

                {showCoordinateGrid && (
                    <div>
                        <label className="block text-xs mb-1">
                            Grid Spacing:
                        </label>
                        <select
                            value={gridSpacing}
                            onChange={(e) =>
                                setGridSpacing(Number(e.target.value))
                            }
                            className="text-xs border rounded px-2 py-1 w-full"
                        >
                            <option value={1}>1°</option>
                            <option value={2}>2°</option>
                            <option value={5}>5°</option>
                            <option value={10}>10°</option>
                            <option value={15}>15°</option>
                            <option value={30}>30°</option>
                        </select>
                    </div>
                )}
            </div>
        </div>
    );
}

export default GridControls;
