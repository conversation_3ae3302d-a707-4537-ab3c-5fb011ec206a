import React, { useEffect } from "react";
import { useMap } from "react-leaflet";
import L from "leaflet";

function GridOverlay({
    gridSpacing = 10, // Grid spacing in degrees
    showGrid = true,
    gridColor = "#666666",
    gridWeight = 1,
    gridOpacity = 0.5,
}) {
    const map = useMap();

    useEffect(() => {
        if (!showGrid) return;

        const gridLines = [];
        const gridLabels = [];

        // Create latitude lines (horizontal)
        for (let lat = -90; lat <= 90; lat += gridSpacing) {
            const latLine = L.polyline(
                [
                    [lat, -180],
                    [lat, 180],
                ],
                {
                    color: gridColor,
                    weight: gridWeight,
                    opacity: gridOpacity,
                    interactive: false,
                }
            );

            gridLines.push(latLine);
            latLine.addTo(map);
        }

        // Create longitude lines (vertical)
        for (let lng = -180; lng <= 180; lng += gridSpacing) {
            const lngLine = L.polyline(
                [
                    [-90, lng],
                    [90, lng],
                ],
                {
                    color: gridColor,
                    weight: gridWeight,
                    opacity: gridOpacity,
                    interactive: false,
                }
            );

            gridLines.push(lngLine);
            lngLine.addTo(map);
        }

        // Cleanup function
        return () => {
            gridLines.forEach((line) => map.removeLayer(line));
            gridLabels.forEach((label) => map.removeLayer(label));
        };
    }, [map, gridSpacing, showGrid, gridColor, gridWeight, gridOpacity]);

    return null;
}

export default GridOverlay;
