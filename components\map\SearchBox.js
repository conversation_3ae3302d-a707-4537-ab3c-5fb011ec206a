import React from "react";

function SearchBox({
    searchTerm,
    setSearchTerm,
    suggestions,
    setSuggestions,
    selectedCountry,
    setSelectedCountry,
    setClickedCountry,
    handleGoToCountry
}) {
    return (
        <div className="mb-4 w-[50%] mx-auto relative">
            <div className="flex">
                <input
                    type="text"
                    onKeyDown={(e) => {
                        if (e.key === "Enter") {
                            e.preventDefault();
                            setClickedCountry(
                                suggestions.length > 0
                                    ? suggestions[0]
                                    : ""
                            );
                            handleGoToCountry();
                        } else if (
                            /^[a-zA-Z]$/.test(e.key) &&
                            searchTerm.trim() === selectedCountry
                        ) {
                            setSearchTerm("");
                        }
                    }}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Enter country name..."
                    className="flex-grow p-2 border border-gray-300 rounded-l focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                    onClick={handleGoToCountry}
                    className="bg-blue-500 text-white px-4 py-2 rounded-r hover:bg-blue-600 transition"
                >
                    Go To
                </button>
            </div>

            {/* Suggestions dropdown */}
            {suggestions.length > 0 && (
                <ul className="absolute z-10 w-full bg-white border border-gray-300 rounded-b shadow-lg max-h-60 overflow-auto">
                    {suggestions.map((suggestion) => (
                        <li
                            key={suggestion}
                            className="p-2 hover:bg-gray-100 cursor-pointer"
                            onClick={() => {
                                setSearchTerm(suggestion);
                                setSelectedCountry(suggestion);
                                setSuggestions([]);
                            }}
                        >
                            {suggestion}
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
}

export default SearchBox;
