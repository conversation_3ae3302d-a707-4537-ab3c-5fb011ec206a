import { useEffect } from "react";
import { useMap } from "react-leaflet";
import L from "leaflet";

// This component accesses the map instance for programmatic control
function MapController({ selectedCountry, countriesData }) {
    const map = useMap();

    useEffect(() => {
        if (selectedCountry && countriesData) {
            // Find the selected country in the GeoJSON data
            const country = countriesData.features.find(
                (f) => f.properties.name === selectedCountry
            );

            if (country) {
                // Create a GeoJSON layer for just this country to get its bounds
                const layer = L.geoJSON(country);
                // Zoom the map to fit the country bounds with some padding
                map.fitBounds(layer.getBounds(), { padding: [20, 20] });
            }
        }
    }, [selectedCountry, countriesData, map]);

    return null;
}

export default MapController;
