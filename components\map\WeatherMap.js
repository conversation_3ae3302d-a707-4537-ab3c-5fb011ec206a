import React, { useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>eoJ<PERSON><PERSON> } from "react-leaflet";
import L from "leaflet";
import MapController from "./MapController";

function WeatherMap({
    selectedCountry,
    setSelectedCountry,
    setSearchTerm,
    setClickedCountry,
    setSuggestions,
    countriesData,
    mapTime,
    setClickedCoordinates,
}) {
    useEffect(() => {
        // Fix icon issues
        delete L.Icon.Default.prototype._getIconUrl;
        L.Icon.Default.mergeOptions({
            iconRetinaUrl:
                "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",
            iconUrl:
                "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",
            shadowUrl:
                "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png",
        });
    }, []);

    // Style for countries
    const countryStyle = {
        fillColor: "#cccccc",
        weight: 2, // Erhöhte Linienbreite
        opacity: 1,
        color: "#5f93ad",
        fillOpacity: 1, // Angepasste Transparenz
        smoothFactor: 0.5, // Reduzierter smoothFactor für mehr Details
    };

    // Style for selected country
    const selectedCountryStyle = {
        fillColor: "#3388ff",
        weight: 2, // Erhöhte Linienbreite
        opacity: 1,
        color: "#5f93ad",
        fillOpacity: 0, // Höhere Deckkraft
        smoothFactor: 0.3, // Noch niedrigerer smoothFactor für mehr Details
    };

    // Event handlers for GeoJSON layer
    const onEachCountry = (country, layer) => {
        const countryName = country.properties.name;
        layer.bindPopup(countryName);

        layer.on({
            click: (e) => {
                // Get the clicked coordinates (latitude and longitude)
                const { lat, lng } = e.latlng;

                // Fix the typo in the function name
                setClickedCoordinates([lat, lng]);

                // Set other state values as before
                setSelectedCountry(country.properties.name);
                setSearchTerm(country.properties.name);
                setClickedCountry(country.properties.name);
                setSuggestions([]);
            },
        });
    };

    // Style function that applies different styles based on selection
    const style = (feature) => {
        return feature.properties.name === selectedCountry
            ? selectedCountryStyle
            : countryStyle;
    };

    return (
        <div className="h-[65vh] overflow-hidden">
            <MapContainer
                center={[20, 0]}
                zoom={2}
                scrollWheelZoom={true}
                style={{ height: "100%", width: "100%" }}
                // Höhere Auflösung für Retina-Displays
                preferCanvas={true}
                // Bessere Performance und Darstellung
                renderer={L.canvas({ padding: 0.5, tolerance: 10 })}
            >
                <TileLayer
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                />
                <TileLayer
                    attribution='&copy; <a href="https://openweathermap.org/">OpenWeatherMap</a>'
                    url={
                        "https://tile.openweathermap.org/map/temp_new/{z}/{x}/{y}.png?appid=c8fe19cc671d0a73f6970f6aa5e4f432"
                    }
                    opacity={1}
                />

                {countriesData && (
                    <GeoJSON
                        data={countriesData}
                        style={style}
                        onEachFeature={onEachCountry}
                        key={selectedCountry}
                        // Präzisere Darstellung der Polygone
                        smoothFactor={0.5}
                        // Bessere Darstellung bei hohen Zoom-Stufen
                        precision={6}
                    />
                )}

                {/* Controller component for programmatic map manipulation */}
                {countriesData && (
                    <MapController
                        selectedCountry={selectedCountry}
                        countriesData={countriesData}
                    />
                )}
            </MapContainer>
        </div>
    );
}

export default WeatherMap;
