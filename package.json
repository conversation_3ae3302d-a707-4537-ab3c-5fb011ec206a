{"name": "weathermap", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/geojson": "^7946.0.16", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-leaflet": "^5.0.0-rc.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4"}}