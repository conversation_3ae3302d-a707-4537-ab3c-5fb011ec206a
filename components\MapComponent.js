import React, { useEffect, useState } from "react";
import SearchBox from "./map/SearchBox";
import TimeMachine from "./map/TimeMachine";
import CountryInfoPanel from "./map/CountryInfoPanel";
import WeatherMap from "./map/WeatherMap";
import { useMap } from "react-leaflet";

function drawGrid(map, precision = 0.1) {
    // Karte auslesen
    const bounds = map.getBounds();
    const south = bounds.getSouth();
    const north = bounds.getNorth();
    const west = bounds.getWest();
    const east = bounds.getEast();

    // Raster bereinigen (optional)
    if (window._gridLayers) {
        window._gridLayers.forEach((layer) => map.removeLayer(layer));
    }
    window._gridLayers = [];

    // Über Karte iterieren
    for (
        let lat = Math.floor(south / precision) * precision;
        lat < north;
        lat += precision
    ) {
        for (
            let lon = Math.floor(west / precision) * precision;
            lon < east;
            lon += precision
        ) {
            const rectBounds = [
                [lat, lon],
                [lat + precision, lon + precision],
            ];

            const rect = L.rectangle(rectBounds, {
                color: "#ff7800",
                weight: 1,
                fillOpacity: 0.05,
            }).addTo(map);

            window._gridLayers.push(rect);
        }
    }
}

function MapComponent() {
    const [currentTimeSelected, setCurrentTimeSelected] = useState(0);
    const [selectedCountry, setSelectedCountry] = useState(null);
    const [countriesData, setCountriesData] = useState(null);
    const [clickedCountry, setClickedCountry] = useState(null);
    const [searchTerm, setSearchTerm] = useState("");
    const [suggestions, setSuggestions] = useState([]);
    const [mapTime, setMapTime] = useState(null);
    const [clickedCoordinates, setClickedCoordinates] = useState([]);

    useEffect(() => {
        // Fetch GeoJSON data for countries
        fetch(
            "https://raw.githubusercontent.com/johan/world.geo.json/master/countries.geo.json"
        )
            .then((response) => response.json())
            .then((data) => {
                setCountriesData(data);
            })
            .catch((error) => console.error("Error fetching GeoJSON:", error));
    }, []);

    useEffect(() => {
        const map = useMap();
        map.whenReady(() => drawGrid(map, 0.1));

        // Bei jedem Verschieben oder Zoomen neu zeichnen
        map.on("moveend", () => drawGrid(map, 0.1));
    }, []);

    // Update suggestions when search term changes
    useEffect(() => {
        if (searchTerm.trim() === "" || !countriesData) {
            setSuggestions([]);
            return;
        }

        const term = searchTerm.toLowerCase();

        if (clickedCountry && clickedCountry.toLowerCase() === term) {
            setSuggestions([]);
            return;
        }

        const matches = countriesData.features
            .filter((country) =>
                country.properties.name.toLowerCase().includes(term)
            )
            .map((country) => country.properties.name)
            .slice(0, 5); // Limit to 5 suggestions

        setSuggestions(matches);
    }, [searchTerm, countriesData, clickedCountry]);

    // Handle search submission
    const handleGoToCountry = () => {
        if (!countriesData) return;

        // Find exact match or first suggestion
        const country =
            countriesData.features.find(
                (f) =>
                    f.properties.name.toLowerCase() === searchTerm.toLowerCase()
            ) ||
            (suggestions.length > 0 &&
                countriesData.features.find(
                    (f) => f.properties.name === suggestions[0]
                ));

        if (country) {
            setSelectedCountry(country.properties.name);
            setSearchTerm(country.properties.name);
            setSuggestions([]);
        }
    };

    return (
        <div>
            <div className="flex flex-row">
                <div className="flex flex-col flex-2/3">
                    <WeatherMap
                        selectedCountry={selectedCountry}
                        setSelectedCountry={setSelectedCountry}
                        setSearchTerm={setSearchTerm}
                        setClickedCountry={setClickedCountry}
                        setSuggestions={setSuggestions}
                        countriesData={countriesData}
                        mapTime={mapTime}
                        setClickedCoordinates={setClickedCoordinates}
                    />
                    <TimeMachine
                        currentTimeSelected={currentTimeSelected}
                        setCurrentTimeSelected={setCurrentTimeSelected}
                    />
                </div>

                <div className="h-[50vh] flex-1/3">
                    <SearchBox
                        searchTerm={searchTerm}
                        setSearchTerm={setSearchTerm}
                        suggestions={suggestions}
                        setSuggestions={setSuggestions}
                        selectedCountry={selectedCountry}
                        setSelectedCountry={setSelectedCountry}
                        setClickedCountry={setClickedCountry}
                        handleGoToCountry={handleGoToCountry}
                    />
                    <CountryInfoPanel
                        selectedCountry={selectedCountry}
                        clickedCoordinates={clickedCoordinates}
                    />
                </div>
            </div>
        </div>
    );
}

export default MapComponent;
