import React, { useEffect, useState, useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, GeoJSON, useMap } from "react-leaflet";
import L from "leaflet";

// This component accesses the map instance for programmatic control
function MapController({ selectedCountry, countriesData }) {
    const map = useMap();

    useEffect(() => {
        if (selectedCountry && countriesData) {
            // Find the selected country in the GeoJSON data
            const country = countriesData.features.find(
                (f) => f.properties.name === selectedCountry
            );

            if (country) {
                // Create a GeoJSON layer for just this country to get its bounds
                const layer = L.geoJSON(country);
                // Zoom the map to fit the country bounds with some padding
                map.fitBounds(layer.getBounds(), { padding: [20, 20] });
            }
        }
    }, [selectedCountry, countriesData, map]);

    return null;
}

function MapComponent() {
    const [currentTimeSelected, setCurrentTimeSelected] = useState(0);
    const [countryInformation, setCountryInformation] = useState(null);
    const [selectedCountry, setSelectedCountry] = useState(null);
    const [countriesData, setCountriesData] = useState(null);
    const [hoveredCountry, setHoveredCountry] = useState(null);
    const [clickedCountry, setClickedCountry] = useState(null);
    const [searchTerm, setSearchTerm] = useState("");
    const [suggestions, setSuggestions] = useState([]);
    const mapRef = useRef(null);

    useEffect(() => {
        fetch(`https://restcountries.com/v3.1/name/${selectedCountry}`)
            .then((response) => response.json())
            .then((data) => {
                setCountryInformation(data[0]);
            });
    }, [selectedCountry]);

    useEffect(() => {
        console.log(countryInformation);
    }, [countryInformation]);

    useEffect(() => {
        // Fix icon issues
        delete L.Icon.Default.prototype._getIconUrl;
        L.Icon.Default.mergeOptions({
            iconRetinaUrl:
                "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",
            iconUrl:
                "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",
            shadowUrl:
                "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png",
        });

        // Fetch GeoJSON data for countries
        fetch(
            "https://raw.githubusercontent.com/johan/world.geo.json/master/countries.geo.json"
        )
            .then((response) => response.json())
            .then((data) => {
                setCountriesData(data);
                console.log(data);
            })
            .catch((error) => console.error("Error fetching GeoJSON:", error));
    }, []);

    // Update suggestions when search term changes
    useEffect(() => {
        if (searchTerm.trim() === "" || !countriesData) {
            setSuggestions([]);
            return;
        }

        const term = searchTerm.toLowerCase();

        if (clickedCountry && clickedCountry.toLowerCase() === term) {
            setSuggestions([]);
            return;
        }

        const matches = countriesData.features
            .filter((country) =>
                country.properties.name.toLowerCase().includes(term)
            )
            .map((country) => country.properties.name)
            .slice(0, 5); // Limit to 5 suggestions

        setSuggestions(matches);
    }, [searchTerm, countriesData]);

    // Handle search submission
    const handleGoToCountry = () => {
        if (!countriesData) return;

        // Find exact match or first suggestion
        const country =
            countriesData.features.find(
                (f) =>
                    f.properties.name.toLowerCase() === searchTerm.toLowerCase()
            ) ||
            (suggestions.length > 0 &&
                countriesData.features.find(
                    (f) => f.properties.name === suggestions[0]
                ));

        if (country) {
            setSelectedCountry(country.properties.name);
            setSearchTerm(country.properties.name);
            setSuggestions([]);
        }
    };

    // Style for countries
    const countryStyle = {
        fillColor: "#cccccc",
        weight: 1.5, // Erhöhte Linienbreite
        opacity: 1,
        color: "white",
        fillOpacity: 0.9, // Angepasste Transparenz
        smoothFactor: 0.5, // Reduzierter smoothFactor für mehr Details
    };

    // Style for selected country
    const selectedCountryStyle = {
        fillColor: "#3388ff",
        weight: 2.5, // Dickere Linie für ausgewähltes Land
        opacity: 1,
        color: "#666",
        fillOpacity: 0, // Höhere Deckkraft
        smoothFactor: 0.3, // Noch niedrigerer smoothFactor für mehr Details
    };

    // Event handlers for GeoJSON layer
    const onEachCountry = (country, layer) => {
        const countryName = country.properties.name;
        layer.bindPopup(countryName);

        layer.on({
            click: () => {
                setSelectedCountry(country.properties.name);
                setSearchTerm(country.properties.name);
                setClickedCountry(country.properties.name);
                setSuggestions([]);
            },
        });
    };

    // Style function that applies different styles based on selection
    const style = (feature) => {
        return feature.properties.name === selectedCountry
            ? selectedCountryStyle
            : countryStyle;
    };

    return (
        <div>
            <div className="flex flex-row">
                <div className="flex flex-col">
                    <div className="h-[50vh] w-[40vw] overflow-hidden">
                        <MapContainer
                            center={[20, 0]}
                            zoom={2}
                            scrollWheelZoom={true}
                            style={{ height: "100%", width: "100%" }}
                            ref={mapRef}
                            // Höhere Auflösung für Retina-Displays
                            preferCanvas={true}
                            // Bessere Performance und Darstellung
                            renderer={L.canvas({ padding: 0.5, tolerance: 10 })}
                        >
                            <TileLayer
                                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                            />
                            <TileLayer
                                attribution='&copy; <a href="https://openweathermap.org/">OpenWeatherMap</a>'
                                url={
                                    "https://tile.openweathermap.org/map/temp_new/{z}/{x}/{y}.png?appid=c8fe19cc671d0a73f6970f6aa5e4f432"
                                }
                                opacity={1}
                            />

                            {countriesData && (
                                <GeoJSON
                                    data={countriesData}
                                    style={style}
                                    onEachFeature={onEachCountry}
                                    key={selectedCountry}
                                    // Präzisere Darstellung der Polygone
                                    smoothFactor={0.5}
                                    // Bessere Darstellung bei hohen Zoom-Stufen
                                    precision={6}
                                />
                            )}

                            {/* Controller component for programmatic map manipulation */}
                            {countriesData && (
                                <MapController
                                    selectedCountry={selectedCountry}
                                    countriesData={countriesData}
                                />
                            )}
                        </MapContainer>
                    </div>
                    <div>
                        <h1>Timemachine</h1>
                        <input
                            type="range"
                            min={0}
                            max={Date.now()}
                            onChange={(e) => setCurrentTime}
                        />
                        <h1>{currentTimeSelected}</h1>
                    </div>
                </div>

                <div className="h-[50vh] flex-1/2">
                    {/* Search Box */}
                    <div className="mb-4 w-[50%] mx-auto relative">
                        <div className="flex">
                            <input
                                type="text"
                                onKeyDown={(e) => {
                                    if (e.key === "Enter") {
                                        e.preventDefault();
                                        setClickedCountry(
                                            suggestions.length > 0
                                                ? suggestions[0]
                                                : ""
                                        );
                                        handleGoToCountry();
                                    } else if (
                                        /^[a-zA-Z]$/.test(e.key) &&
                                        searchTerm.trim() === selectedCountry
                                    ) {
                                        setSearchTerm("");
                                    }
                                }}
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                placeholder="Enter country name..."
                                className="flex-grow p-2 border border-gray-300 rounded-l focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                            <button
                                onClick={handleGoToCountry}
                                className="bg-blue-500 text-white px-4 py-2 rounded-r hover:bg-blue-600 transition"
                            >
                                Go To
                            </button>
                        </div>

                        {/* Suggestions dropdown */}
                        {suggestions.length > 0 && (
                            <ul className="absolute z-10 w-full bg-white border border-gray-300 rounded-b shadow-lg max-h-60 overflow-auto">
                                {suggestions.map((suggestion) => (
                                    <li
                                        key={suggestion}
                                        className="p-2 hover:bg-gray-100 cursor-pointer"
                                        onClick={() => {
                                            setSearchTerm(suggestion);
                                            setSelectedCountry(suggestion);
                                            setSuggestions([]);
                                        }}
                                    >
                                        {suggestion}
                                    </li>
                                ))}
                            </ul>
                        )}
                    </div>
                    <div className="w-full bg-white h-[100vh] overflow-y-auto p-4 border border-gray-300 rounded-md shadow-md">
                        {selectedCountry ? (
                            <div className="space-y-4">
                                <h1 className="text-2xl font-bold text-gray-800">
                                    {selectedCountry}
                                </h1>

                                {countriesData &&
                                    countriesData.features
                                        .filter(
                                            (country) =>
                                                country.properties.name ===
                                                selectedCountry
                                        )
                                        .map((country) => (
                                            <div
                                                key={country.properties.name}
                                                className="space-y-4"
                                            >
                                                {/* Basic Information */}
                                                <div className="bg-gray-50 p-3 rounded-lg">
                                                    <h2 className="text-lg font-semibold text-gray-700 border-b pb-1 mb-2">
                                                        Basic Information
                                                    </h2>
                                                    <div className="grid grid-cols-2 gap-2 text-sm">
                                                        <div className="font-medium">
                                                            Official Name:
                                                        </div>
                                                        <div>
                                                            {country.properties
                                                                .name_long ||
                                                                country
                                                                    .properties
                                                                    .name}
                                                        </div>

                                                        <div className="font-medium">
                                                            ISO Codes:
                                                        </div>
                                                        <div>
                                                            {
                                                                country
                                                                    .properties
                                                                    .iso_a2
                                                            }
                                                            /
                                                            {country.properties
                                                                .iso_a3 ||
                                                                "N/A"}
                                                        </div>

                                                        <div className="font-medium">
                                                            Region:
                                                        </div>
                                                        <div>
                                                            {country.properties
                                                                .region ||
                                                                country
                                                                    .properties
                                                                    .continent ||
                                                                "N/A"}
                                                        </div>

                                                        <div className="font-medium">
                                                            Subregion:
                                                        </div>
                                                        <div>
                                                            {country.properties
                                                                .subregion ||
                                                                "N/A"}
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Geography */}
                                                <div className="bg-blue-50 p-3 rounded-lg">
                                                    <h2 className="text-lg font-semibold text-gray-700 border-b pb-1 mb-2">
                                                        Geography
                                                    </h2>
                                                    <div className="grid grid-cols-2 gap-2 text-sm">
                                                        <div className="font-medium">
                                                            Area:
                                                        </div>
                                                        <div>
                                                            {country.properties
                                                                .area_km2 ||
                                                                "Calculating..."}{" "}
                                                            km²
                                                        </div>

                                                        <div className="font-medium">
                                                            Capital:
                                                        </div>
                                                        <div>
                                                            {country.properties
                                                                .capital ||
                                                                "N/A"}
                                                        </div>

                                                        <div className="font-medium">
                                                            Borders:
                                                        </div>
                                                        <div>
                                                            {country.properties
                                                                .borders
                                                                ? country.properties.borders.join(
                                                                      ", "
                                                                  )
                                                                : "Island nation"}
                                                        </div>

                                                        <div className="font-medium">
                                                            Coastline:
                                                        </div>
                                                        <div>
                                                            {country.properties
                                                                .coastline
                                                                ? "Yes"
                                                                : "Landlocked"}
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Demographics */}
                                                <div className="bg-green-50 p-3 rounded-lg">
                                                    <h2 className="text-lg font-semibold text-gray-700 border-b pb-1 mb-2">
                                                        Demographics
                                                    </h2>
                                                    <div className="grid grid-cols-2 gap-2 text-sm">
                                                        <div className="font-medium">
                                                            Population:
                                                        </div>
                                                        <div>
                                                            {country.properties
                                                                .pop_est
                                                                ? new Intl.NumberFormat().format(
                                                                      country
                                                                          .properties
                                                                          .pop_est
                                                                  )
                                                                : "N/A"}
                                                        </div>

                                                        <div className="font-medium">
                                                            Languages:
                                                        </div>
                                                        <div>
                                                            {country.properties
                                                                .languages ||
                                                                "N/A"}
                                                        </div>

                                                        <div className="font-medium">
                                                            Currency:
                                                        </div>
                                                        <div>
                                                            {country.properties
                                                                .currency ||
                                                                "N/A"}
                                                        </div>

                                                        <div className="font-medium">
                                                            GDP:
                                                        </div>
                                                        <div>
                                                            {country.properties
                                                                .gdp_md_est
                                                                ? `$${country.properties.gdp_md_est} million`
                                                                : "N/A"}
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Weather & Climate (would require API integration) */}
                                                <div className="bg-yellow-50 p-3 rounded-lg">
                                                    <h2 className="text-lg font-semibold text-gray-700 border-b pb-1 mb-2">
                                                        Weather & Climate
                                                    </h2>
                                                    <div className="flex justify-between items-center">
                                                        <div className="text-sm">
                                                            <p>
                                                                Current weather
                                                                data would be
                                                                fetched from a
                                                                weather API.
                                                            </p>
                                                            <p className="mt-1">
                                                                Possible data:
                                                                temperature,
                                                                conditions,
                                                                forecast,
                                                                climate zones
                                                            </p>
                                                        </div>
                                                        <button
                                                            className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition"
                                                            onClick={() =>
                                                                alert(
                                                                    `Would fetch weather for ${selectedCountry}`
                                                                )
                                                            }
                                                        >
                                                            Get Weather
                                                        </button>
                                                    </div>
                                                </div>

                                                {/* External Resources */}
                                                <div className="bg-purple-50 p-3 rounded-lg">
                                                    <h2 className="text-lg font-semibold text-gray-700 border-b pb-1 mb-2">
                                                        Learn More
                                                    </h2>
                                                    <div className="flex flex-wrap gap-2">
                                                        <a
                                                            href={`https://en.wikipedia.org/wiki/${selectedCountry}`}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            className="px-3 py-1 bg-gray-200 text-gray-800 text-sm rounded hover:bg-gray-300 transition"
                                                        >
                                                            Wikipedia
                                                        </a>
                                                        <a
                                                            href={`https://www.google.com/maps/place/${selectedCountry}`}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            className="px-3 py-1 bg-gray-200 text-gray-800 text-sm rounded hover:bg-gray-300 transition"
                                                        >
                                                            Google Maps
                                                        </a>
                                                        <a
                                                            href={`https://www.cia.gov/the-world-factbook/countries/${selectedCountry
                                                                .toLowerCase()
                                                                .replace(
                                                                    /\s+/g,
                                                                    "-"
                                                                )}/`}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            className="px-3 py-1 bg-gray-200 text-gray-800 text-sm rounded hover:bg-gray-300 transition"
                                                        >
                                                            CIA World Factbook
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                            </div>
                        ) : (
                            <div className="flex flex-col items-center justify-center h-full text-gray-500">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-12 w-12 mb-2"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
                                    />
                                </svg>
                                <p className="text-lg font-medium">
                                    Select a country on the map
                                </p>
                                <p className="text-sm">
                                    Click on any country to view detailed
                                    information
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default MapComponent;
