import React from "react";

function TimeMachine({ currentTimeSelected, setCurrentTimeSelected }) {
    return (
        <div className="mx-auto my-10 w-full">
            <h1>Timemachine</h1>
            <input
                className="w-full"
                type="range"
                min={0}
                max={Date.now()}
                value={currentTimeSelected}
                onChange={(e) => setCurrentTimeSelected(Number(e.target.value))}
            />
            <h1>{new Date(currentTimeSelected).toDateString()}</h1>
            <button
                onClick={() => {
                    fetch(
                        `https://api.openweathermap.org/data/3.0/onecall/timemachine?lat=20&lon=20&dt=${Math.floor(
                            currentTimeSelected / 1000
                        )}&appid=9c1bce6b39985c1e22c7be5fd54cebb1`
                    )
                        .then((response) => response.json())
                        .then((data) => console.log(data));
                }}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition"
            >
                Chrono break!
            </button>
        </div>
    );
}

export default TimeMachine;
