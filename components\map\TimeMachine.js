import React from "react";

function TimeMachine({
    currentTimeSelected,
    setCurrentTimeSelected,
    setMapTime,
}) {
    return (
        <div className="mx-auto my-10 w-full">
            <h1>Timemachine</h1>
            <input
                className="w-full"
                type="range"
                min={0}
                max={Date.now()}
                value={currentTimeSelected}
                onChange={(e) => setCurrentTimeSelected(Number(e.target.value))}
            />
            <h1>{new Date(currentTimeSelected).toDateString()}</h1>
            <button
                onClick={() => {}}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition"
            >
                Chrono break!
            </button>
        </div>
    );
}

export default TimeMachine;
